datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// prisma/schema.prisma
model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  admin         <PERSON>   @default(false)
  accounts      Account[]
}

model Account {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  userId            String   @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.String
  access_token      String?  @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.String
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Announcement {
  id          String   @id @default(uuid()) @map("_id")
  title       String
  description String
  footer      String?
  imageUrl    String?
  createdAt   DateTime @default(now())
}

model PartnershipApplication {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  username    String
  companyName String
  websiteUrl  String
  reason      String
  experience  String
  additional  String?
  status      String   @default("pending") // 'pending', 'accepted', 'rejected'
  appliedAt   DateTime @default(now())
  statusUpdatedAt DateTime?
}

model StaffApplication {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  userId          String   @unique @db.ObjectId
  fullName        String
  age             Int
  location        String
  availability    String // 'full-time', 'part-time', 'weekends'
  position        String // 'moderator', 'developer', 'designer', 'support', 'other'
  experience      String
  skills          String
  motivation      String
  portfolioUrl    String?
  additionalInfo  String?
  status          String   @default("pending") // 'pending', 'accepted', 'rejected'
  appliedAt       DateTime @default(now())
  statusUpdatedAt DateTime?
}

model Plugin {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  publisherId     String    @db.ObjectId
  publisherName   String
  name            String
  description     String
  version         String
  releaseType     String    @default("release") // "release" or "snapshot"
  releaseDate     DateTime  @default(now())
  updateDate      DateTime  @updatedAt
  fileLocation    String
  downloads       Int       @default(0)
  comments        PluginComment[]
}

model PluginComment {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  pluginId        String    @db.ObjectId
  userId          String    @db.ObjectId
  userName        String
  content         String
  createdAt       DateTime  @default(now())
  plugin          Plugin    @relation(fields: [pluginId], references: [id], onDelete: Cascade)
}



