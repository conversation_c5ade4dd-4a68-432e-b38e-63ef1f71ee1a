import NextAuth from "next-auth";
import { NextResponse } from "next/server";

import { authConfig } from "@/lib/auth.config";
import { API_AUTH_PREFIX, AUTH_ROUTES, PROTECTED_ROUTES, DEVELOPER_ROUTES } from "@/routes";

export const { auth } = NextAuth(authConfig);

export default auth(async (req) => {
  const pathname = req.nextUrl.pathname;

  const isAuth = req.auth;
  const email = req.auth?.user?.email;

  console.log(`Middleware - Path: ${pathname}, Auth: ${!!isAuth}, Email: ${email}`);

  const isAccessingApiAuthRoute = pathname.startsWith(API_AUTH_PREFIX);
  const isAccessingProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));
  const isAccessingAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));
  const isAccessingAdminRoute = pathname.startsWith("/admin");


  if (isAccessingApiAuthRoute) {
    return NextResponse.next();
  }

  if (isAccessingAdminRoute) {
    if (!isAuth) {
      console.log('Middleware - Not authenticated, redirecting to login.');
      return NextResponse.redirect(new URL("/missing-permission", req.url));
    }

    // Check admin status directly using the email
      // For admin routes, we'll only block users we know are not admins
      // If we can't determine the status, we'll allow access and let client-side handle it
      if (email) {
        const res = await fetch(`${req.nextUrl.origin}/api/admin-check`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });

        if (res.ok) {
          const data = await res.json();
          console.log('Middleware - Admin check result:', data);

          if (data.admin === false) {
            console.log('Middleware - Confirmed not admin, redirecting to missing-permission');
            return NextResponse.redirect(new URL("/missing-permission", req.url));
          }
        }
    }
    return NextResponse.next();
  }



  if (isAccessingProtectedRoute) {
    if (!isAuth) {
      return NextResponse.redirect(new URL("/missing-permission", req.url));
    }
    return NextResponse.next();
  }

  if (isAccessingAuthRoute) {
    if (isAuth) {
      return NextResponse.redirect(new URL("/", req.url));
    }
    return NextResponse.next();
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
