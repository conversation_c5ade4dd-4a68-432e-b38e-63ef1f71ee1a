/*
 * External API Client for AveHub Developer Portal
 * Handles communication with the external API documented in api.md
 */

const API_BASE_URL = 'https://developer.avehubs.com';
const API_KEY = 'AIzaSyDhDX8sNRrzKDjjxYNPeQCS2BjvtFK-ZzI';

// API Response Types based on api.md
export interface ApiApp {
  id: string;
  name: string;
  description: string;
  version: string;
  category: string;
  downloads: number;
  fileSize: number;
  iconUrl: string;
  screenshots: string[];
  developer: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ApiAppDetailed extends ApiApp {
  longDescription: string;
  tags: string[];
  rating: number;
  reviewCount: number;
  fileSizeFormatted: string;
  developer: {
    id: string;
    name: string;
    email: string;
    website?: string;
    totalApps: number;
    totalDownloads: number;
  };
  systemRequirements: {
    minWindows: string;
    minMacOS: string;
    minLinux: string;
    diskSpace: string;
    ram: string;
  };
  permissions: string[];
  changelog: Array<{
    version: string;
    date: string;
    changes: string[];
  }>;
  supportedLanguages: string[];
  website?: string;
  supportEmail?: string;
  privacyPolicy?: string;
  license: string;
  publishedAt: string;
}

export interface ApiAppsListResponse {
  success: boolean;
  apps: ApiApp[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalApps: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  filters: {
    search: string | null;
    category: string | null;
    sortBy: string;
    sortOrder: string;
  };
}

export interface ApiAppInfoResponse {
  success: boolean;
  app: ApiAppDetailed;
}

export interface ApiDownloadResponse {
  success: boolean;
  app: {
    id: string;
    name: string;
    version: string;
    fileSize: number;
    developer: {
      id: string;
      name: string;
    };
    lastModified: string;
  };
  download: {
    directUrl: string;
    streamingUrl: string;
    fileName: string;
    contentType: string;
    supportsRangeRequests: boolean;
  };
  downloads: number;
  timestamp: string;
  etag: string;
}

// API Client Class
export class AveHubApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor(baseUrl: string = API_BASE_URL, apiKey: string = API_KEY) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  private getHeaders(): HeadersInit {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error ${response.status}: ${errorText}`);
    }
    return response.json();
  }

  // List Apps API
  async listApps(params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}): Promise<ApiAppsListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.category) searchParams.append('category', params.category);
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const url = `${this.baseUrl}/app/list?${searchParams.toString()}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    return this.handleResponse<ApiAppsListResponse>(response);
  }

  // Get App Info API
  async getAppInfo(appId: string): Promise<ApiAppInfoResponse> {
    const url = `${this.baseUrl}/app/info/${appId}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    return this.handleResponse<ApiAppInfoResponse>(response);
  }

  // Get Download Info API
  async getDownloadInfo(appId: string): Promise<ApiDownloadResponse> {
    const url = `${this.baseUrl}/app/download/${appId}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    return this.handleResponse<ApiDownloadResponse>(response);
  }

  // Direct Download API
  async downloadApp(appId: string, range?: string): Promise<Response> {
    const url = `${this.baseUrl}/app/download/${appId}`;
    const headers: HeadersInit = {
      'Authorization': `Bearer ${this.apiKey}`,
    };

    if (range) {
      headers['Range'] = range;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      throw new Error(`Download failed: ${response.status}`);
    }

    return response;
  }
}

// Mock data for testing when external API is not available
const MOCK_APPS: ApiApp[] = [
  {
    id: "app-1",
    name: "Task Manager Pro",
    description: "A powerful task management application with team collaboration features",
    version: "2.1.0",
    category: "Productivity",
    downloads: 15420,
    fileSize: 25600000,
    iconUrl: "https://via.placeholder.com/128x128/4F46E5/FFFFFF?text=TM",
    screenshots: [
      "https://via.placeholder.com/800x600/4F46E5/FFFFFF?text=Screenshot+1",
      "https://via.placeholder.com/800x600/059669/FFFFFF?text=Screenshot+2"
    ],
    developer: {
      id: "dev-1",
      name: "ProductiveTech",
      email: "<EMAIL>"
    },
    createdAt: "2023-10-15T10:30:00Z",
    updatedAt: "2024-01-20T14:20:00Z"
  },
  {
    id: "app-2",
    name: "Code Editor Plus",
    description: "Advanced code editor with syntax highlighting and plugin support",
    version: "1.5.2",
    category: "Development",
    downloads: 8750,
    fileSize: 45300000,
    iconUrl: "https://via.placeholder.com/128x128/059669/FFFFFF?text=CE",
    screenshots: [
      "https://via.placeholder.com/800x600/059669/FFFFFF?text=Code+View",
      "https://via.placeholder.com/800x600/DC2626/FFFFFF?text=Debug+Mode"
    ],
    developer: {
      id: "dev-2",
      name: "DevTools Inc",
      email: "<EMAIL>"
    },
    createdAt: "2023-08-22T09:15:00Z",
    updatedAt: "2024-02-10T11:45:00Z"
  },
  {
    id: "app-3",
    name: "Photo Enhancer",
    description: "AI-powered photo enhancement and editing tool",
    version: "3.0.1",
    category: "Graphics",
    downloads: 32100,
    fileSize: 78900000,
    iconUrl: "https://via.placeholder.com/128x128/DC2626/FFFFFF?text=PE",
    screenshots: [
      "https://via.placeholder.com/800x600/DC2626/FFFFFF?text=Edit+Mode",
      "https://via.placeholder.com/800x600/7C3AED/FFFFFF?text=AI+Tools"
    ],
    developer: {
      id: "dev-3",
      name: "Creative Studio",
      email: "<EMAIL>"
    },
    createdAt: "2023-12-05T16:20:00Z",
    updatedAt: "2024-03-01T08:30:00Z"
  }
];

// Mock API Client for testing
class MockAveHubApiClient extends AveHubApiClient {
  async listApps(params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}): Promise<ApiAppsListResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredApps = [...MOCK_APPS];

    // Apply search filter
    if (params.search) {
      filteredApps = filteredApps.filter(app =>
        app.name.toLowerCase().includes(params.search!.toLowerCase()) ||
        app.description.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    // Apply category filter
    if (params.category && params.category !== "All") {
      filteredApps = filteredApps.filter(app => app.category === params.category);
    }

    // Apply sorting
    if (params.sortBy) {
      filteredApps.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (params.sortBy) {
          case 'downloads':
            aValue = a.downloads;
            bValue = b.downloads;
            break;
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case 'createdAt':
            aValue = new Date(a.createdAt);
            bValue = new Date(b.createdAt);
            break;
          default:
            aValue = a.downloads;
            bValue = b.downloads;
        }

        if (params.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    // Apply pagination
    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedApps = filteredApps.slice(startIndex, endIndex);

    return {
      success: true,
      apps: paginatedApps,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(filteredApps.length / limit),
        totalApps: filteredApps.length,
        hasNextPage: endIndex < filteredApps.length,
        hasPreviousPage: page > 1
      },
      filters: {
        search: params.search || null,
        category: params.category || null,
        sortBy: params.sortBy || "downloads",
        sortOrder: params.sortOrder || "desc"
      }
    };
  }

  async getAppInfo(appId: string): Promise<ApiAppInfoResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const app = MOCK_APPS.find(a => a.id === appId);
    if (!app) {
      throw new Error('App not found');
    }

    // Create detailed app info
    const detailedApp: ApiAppDetailed = {
      ...app,
      longDescription: `${app.description}. This is a comprehensive application that provides advanced features for ${app.category.toLowerCase()}. Built with modern technologies and designed for optimal performance.`,
      tags: [app.category.toLowerCase(), "productivity", "modern", "efficient"],
      rating: 4.5,
      reviewCount: Math.floor(app.downloads / 10),
      fileSizeFormatted: `${(app.fileSize / 1024 / 1024).toFixed(1)} MB`,
      developer: {
        ...app.developer,
        website: `https://${app.developer.name.toLowerCase().replace(/\s+/g, '')}.com`,
        totalApps: 3,
        totalDownloads: 50000
      },
      systemRequirements: {
        minWindows: "Windows 10",
        minMacOS: "macOS 10.15",
        minLinux: "Ubuntu 18.04",
        diskSpace: `${Math.ceil(app.fileSize / 1024 / 1024)} MB`,
        ram: "4 GB"
      },
      permissions: [
        "Read/Write file access",
        "Network access",
        "System notifications"
      ],
      changelog: [
        {
          version: app.version,
          date: "2024-03-01",
          changes: [
            "Performance improvements",
            "Bug fixes and stability enhancements",
            "New user interface elements"
          ]
        }
      ],
      supportedLanguages: ["en", "es", "fr", "de"],
      website: `https://${app.developer.name.toLowerCase().replace(/\s+/g, '')}.com`,
      supportEmail: app.developer.email,
      privacyPolicy: `https://${app.developer.name.toLowerCase().replace(/\s+/g, '')}.com/privacy`,
      license: "MIT",
      publishedAt: app.createdAt
    };

    return {
      success: true,
      app: detailedApp
    };
  }

  async getDownloadInfo(appId: string): Promise<ApiDownloadResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));

    const app = MOCK_APPS.find(a => a.id === appId);
    if (!app) {
      throw new Error('App not found');
    }

    return {
      success: true,
      app: {
        id: app.id,
        name: app.name,
        version: app.version,
        fileSize: app.fileSize,
        developer: {
          id: app.developer.id,
          name: app.developer.name
        },
        lastModified: app.updatedAt
      },
      download: {
        directUrl: `https://cdn.example.com/apps/${app.id}/${app.name.replace(/\s+/g, '_')}_v${app.version}.zip`,
        streamingUrl: `/api/apps/${app.id}/download`,
        fileName: `${app.name.replace(/\s+/g, '_')}_v${app.version}.zip`,
        contentType: "application/zip",
        supportsRangeRequests: true
      },
      downloads: app.downloads + 1,
      timestamp: new Date().toISOString(),
      etag: `"${app.id}-${Date.now()}"`
    };
  }
}

// Use mock client for development/testing
export const apiClient = new MockAveHubApiClient();
