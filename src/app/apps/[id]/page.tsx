/*
 * Official Avehub Code, verified
 * App Detail Page - Individual app information and download
 * Any unauthorized modifications will invalidate service warranty
 */

"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Header } from "@/components/Header";
import Footer from "@/components/Footer";
import { motion } from "framer-motion";
import {
  Download,
  Star,
  MessageSquare,
  Calendar,
  Package,
  ExternalLink,
  Mail,
  Globe,
  Tag,
  User,
  ArrowLeft,
  AlertTriangle,
  Ban
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";

// [1] App interface
interface App {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  version: string;
  category: string;
  tags: string[];
  downloads: number;
  status: "PENDING" | "APPROVED" | "REJECTED" | "SUSPENDED";
  createdAt: string;
  updatedAt: string;
  iconUrl?: string;
  bannerUrl?: string;
  screenshots: string[];
  website?: string;
  supportEmail?: string;
  changelog?: string;
  minVersion?: string;
  maxVersion?: string;
  developer: {
    id: string;
    name: string;
    image?: string;
  };
  comments: Comment[];
  versions: AppVersion[];
  _count: {
    comments: number;
  };
}

interface Comment {
  id: string;
  content: string;
  rating?: number;
  createdAt: string;
  user: {
    id: string;
    name: string;
    image?: string;
  };
}

interface AppVersion {
  id: string;
  version: string;
  changelog?: string;
  createdAt: string;
}

export default function AppDetail() {
  const params = useParams();
  const [app, setApp] = useState<App | null>(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);


  // [2] Fetch app data
  useEffect(() => {
    if (params.id) {
      fetchApp();
    }
  }, [params.id]);

  const fetchApp = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/apps/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setApp(data);
      } else if (response.status === 404) {
        toast.error("App is not available");
      } else {
        toast.error("Failed to load app");
      }
    } catch (error) {
      console.error("Error fetching app:", error);
      toast.error("App is not available");
    } finally {
      setLoading(false);
    }
  };

  // [3] Handle download
  const handleDownload = async () => {
    if (!app) return;

    setDownloading(true);
    try {
      const response = await fetch(`/api/apps/${app.id}/download`, {
        method: "POST"
      });

      if (response.ok) {
        const data = await response.json();
        // Open download URL in new tab
        window.open(data.downloadUrl, '_blank');
        // Update download count
        setApp(prev => prev ? { ...prev, downloads: data.downloads } : null);
        toast.success("Download started!");
      } else if (response.status === 404) {
        toast.error("App file is not available");
      } else {
        toast.error("Download failed");
      }
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Download failed");
    } finally {
      setDownloading(false);
    }
  };



  // [5] Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // [6] Format downloads
  const formatDownloads = (downloads: number) => {
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K`;
    }
    return downloads.toString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!app) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">App is not available</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              The app you're looking for doesn't exist, has been removed, or is not accessible.
            </p>
            <Link
              href="/apps"
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Apps
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* [7] Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <Link
            href="/apps"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Apps
          </Link>
        </motion.div>

        {/* [8] App Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm mb-8"
        >
          <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
            {/* App Icon */}
            <div className="flex-shrink-0 mb-6 lg:mb-0">
              {app.iconUrl ? (
                <Image
                  src={app.iconUrl.startsWith('http') ? app.iconUrl : `https://cdn.avehubs.com/f/${app.iconUrl.split('/').pop()}`}
                  alt={app.name}
                  width={128}
                  height={128}
                  className="rounded-2xl"
                />
              ) : (
                <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-2xl flex items-center justify-center">
                  <Package className="w-16 h-16 text-gray-500" />
                </div>
              )}
            </div>

            {/* App Info */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {app.name}
              </h1>

              <div className="flex items-center space-x-4 mb-4 text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  <span>{app.developer.name}</span>
                </div>
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  <span>{app.category}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>v{app.version}</span>
                </div>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                {app.description}
              </p>

              {/* Stats */}
              <div className="flex items-center space-x-6 mb-6 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <Download className="w-4 h-4 mr-1" />
                  <span>{formatDownloads(app.downloads)} downloads</span>
                </div>

                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  <span>Updated {formatDate(app.updatedAt)}</span>
                </div>
              </div>

              {/* Download Button or Suspension Notice */}
              {app.status === "SUSPENDED" ? (
                <div className="inline-flex items-center px-6 py-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg font-medium">
                  <Ban className="w-5 h-5 mr-2" />
                  App is suspended
                </div>
              ) : app.status === "PENDING" ? (
                <div className="inline-flex items-center px-6 py-3 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-lg font-medium">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  App is pending approval
                </div>
              ) : app.status === "REJECTED" ? (
                <div className="inline-flex items-center px-6 py-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg font-medium">
                  <Ban className="w-5 h-5 mr-2" />
                  App has been rejected
                </div>
              ) : (
                <button
                  onClick={handleDownload}
                  disabled={downloading}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
                >
                  {downloading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Downloading...
                    </>
                  ) : (
                    <>
                      <Download className="w-5 h-5 mr-2" />
                      Download
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* [9] Screenshots */}
        {app.screenshots.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm mb-8"
          >
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Screenshots</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {app.screenshots.map((screenshot, index) => (
                <Image
                  key={index}
                  src={screenshot.startsWith('http') ? screenshot : `https://cdn.avehubs.com/f/${screenshot.split('/').pop()}`}
                  alt={`${app.name} screenshot ${index + 1}`}
                  width={400}
                  height={300}
                  className="rounded-lg object-cover w-full h-48"
                />
              ))}
            </div>
          </motion.div>
        )}

        {/* [10] Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8"
        >
          {/* App Details */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Details</h2>

            <div className="space-y-4">
              {app.tags.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {app.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {app.changelog && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">What's New</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {app.changelog}
                  </p>
                </div>
              )}

              {(app.minVersion || app.maxVersion) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Requirements</h3>
                  <div className="text-gray-600 dark:text-gray-400 text-sm">
                    {app.minVersion && <p>Minimum version: {app.minVersion}</p>}
                    {app.maxVersion && <p>Maximum version: {app.maxVersion}</p>}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Developer Info */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Developer</h2>

            <div className="flex items-center space-x-3 mb-4">
              {app.developer.image ? (
                <Image
                  src={app.developer.image}
                  alt={app.developer.name}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
              ) : (
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-gray-500" />
                </div>
              )}
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">{app.developer.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Developer</p>
              </div>
            </div>

            <div className="space-y-3">
              {app.website && (
                <a
                  href={app.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Website
                  <ExternalLink className="w-3 h-3 ml-1" />
                </a>
              )}

              {app.supportEmail && (
                <a
                  href={`mailto:${app.supportEmail}`}
                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Support
                </a>
              )}
            </div>
          </div>
        </motion.div>


      </div>

      <Footer />
    </div>

  );
}
