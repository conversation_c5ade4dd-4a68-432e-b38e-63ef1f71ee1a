/*
 * Official Avehub Code, verified
 * Individual App API Routes - Proxy to external AveHub Developer Portal API
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { apiClient } from "@/lib/api-client";

// [1] GET - Retrieve single app from external API
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Call external API
    const response = await apiClient.getAppInfo(id);

    if (!response.success) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    const app = response.app;

    // Transform the response to match the expected format for the frontend
    const transformedApp = {
      id: app.id,
      name: app.name,
      description: app.longDescription || app.description,
      shortDescription: app.description,
      version: app.version,
      category: app.category,
      tags: app.tags || [],
      downloads: app.downloads,
      status: "APPROVED", // All apps from external API are approved
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      iconUrl: app.iconUrl,
      bannerUrl: null, // Not provided by external API
      screenshots: app.screenshots || [],
      website: app.website,
      supportEmail: app.supportEmail,
      changelog: app.changelog?.length > 0 ? app.changelog[0].changes.join('\n') : null,
      minVersion: app.systemRequirements?.minWindows,
      maxVersion: null,
      developer: {
        id: app.developer.id,
        name: app.developer.name,
        image: null // Not provided by external API
      },
      comments: [], // Comments not supported in this version
      versions: [], // Versions not supported in this version
      _count: {
        comments: 0 // Comments not supported
      }
    };

    return NextResponse.json(transformedApp);
  } catch (error) {
    console.error("Error fetching app:", error);
    return NextResponse.json({ error: "App not found" }, { status: 404 });
  }
}

// PATCH and DELETE methods not supported - apps are managed through external developer portal
export async function PATCH() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
