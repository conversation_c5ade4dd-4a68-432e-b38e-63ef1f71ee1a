/*
 * Official Avehub Code, verified
 * App Download API Route - Proxy to external AveHub Developer Portal API
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { apiClient } from "@/lib/api-client";

// [1] GET - Get download info from external API
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Call external API to get download info
    const response = await apiClient.getDownloadInfo(id);

    if (!response.success) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error getting download info:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// [2] POST - Initiate app download
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get download info first
    const downloadInfo = await apiClient.getDownloadInfo(id);

    if (!downloadInfo.success) {
      return NextResponse.json({ error: "App not found" }, { status: 404 });
    }

    // Return the direct download URL for the frontend to handle
    return NextResponse.json({
      downloadUrl: downloadInfo.download.directUrl,
      downloads: downloadInfo.downloads + 1,
      fileName: downloadInfo.download.fileName
    });
  } catch (error) {
    console.error("Error initiating download:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
