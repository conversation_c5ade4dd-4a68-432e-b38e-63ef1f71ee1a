/*
 * Official Avehub Code, verified
 * Apps API Routes - Proxy to external AveHub Developer Portal API
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import { apiClient } from "@/lib/api-client";

// [1] GET - Retrieve apps from external API
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || undefined;
    const category = searchParams.get("category") || undefined;
    const sortBy = searchParams.get("sortBy") || "downloads";
    const sortOrder = searchParams.get("order") || "desc";
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10;
    const page = searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1;

    // Call external API
    const response = await apiClient.listApps({
      page,
      limit,
      search,
      category: category === "All" ? undefined : category,
      sortBy,
      sortOrder,
    });

    if (!response.success) {
      return NextResponse.json({ error: "Failed to fetch apps" }, { status: 500 });
    }

    // Transform the response to match the expected format for the frontend
    const transformedApps = response.apps.map(app => ({
      id: app.id,
      name: app.name,
      description: app.description,
      shortDescription: app.description, // Use description as shortDescription
      version: app.version,
      category: app.category,
      tags: [], // External API doesn't provide tags in list view
      downloads: app.downloads,
      createdAt: app.createdAt,
      iconUrl: app.iconUrl,
      bannerUrl: null, // Not provided in list view
      developer: {
        id: app.developer.id,
        name: app.developer.name,
        image: null // Not provided in list view
      },
      _count: {
        comments: 0 // Comments not available in list view
      }
    }));

    return NextResponse.json(transformedApps);
  } catch (error) {
    console.error("Error fetching apps:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

// POST method not supported - apps are managed through external developer portal
export async function POST() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
